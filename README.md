# RootData Linea Ecosystem Extractor

A comprehensive, production-ready solution for extracting all Linea ecosystem projects from the RootData API. This tool respects API rate limits, includes robust error handling, and provides detailed logging and monitoring.

## Features

- 🚀 **Complete Linea Ecosystem Extraction**: Automatically finds and extracts all projects in the Linea ecosystem
- 🔒 **Secure Authentication**: Uses API key authentication with the RootData API
- ⚡ **Rate Limiting**: Built-in rate limiter respects the 100 requests/minute API limit
- 🔄 **Retry Logic**: Exponential backoff retry mechanism for transient failures
- 📊 **Comprehensive Data**: Extracts project details, funding info, team members, investors, and more
- 📁 **Multiple Output Formats**: Saves results in both JSON and CSV formats
- 📝 **Detailed Logging**: Comprehensive logging for monitoring and debugging
- 🛡️ **Error Handling**: Robust error handling with specific error types
- 🔧 **Configurable**: Environment-based configuration management

## Installation

1. **Clone or download this project**
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Build the TypeScript code**:
   ```bash
   npm run build
   ```

## Configuration

### Environment Variables

Create a `.env` file in the root directory (copy from `.env.example`):

```env
# RootData API Configuration
ROOTDATA_API_KEY=AtIYXUndk8xNYFzBs9LiGOUhv1z4wGXk
ROOTDATA_LANGUAGE=en

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=10

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY_MS=1000

# Output Configuration
OUTPUT_FORMAT=json
OUTPUT_DIRECTORY=./output

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/rootdata-extractor.log
```

### API Key

The script is pre-configured with the provided API key: `AtIYXUndk8xNYFzBs9LiGOUhv1z4wGXk`

If you need to use a different API key, set the `ROOTDATA_API_KEY` environment variable.

## Usage

### Quick Start

Run the extraction script directly:

```bash
npm run extract-linea
```

Or using ts-node for development:

```bash
npm run dev
```

Or run the compiled JavaScript:

```bash
npm run build
npm start
```

### What the Script Does

1. **Connects to RootData API** and verifies authentication
2. **Finds Linea Ecosystem** by searching the ecosystem map
3. **Retrieves All Projects** in the Linea ecosystem
4. **Gets Detailed Information** for each project including:
   - Basic project info (name, description, logo)
   - Token information and contract addresses
   - Funding details and investor information
   - Team member details
   - Social media links
   - Tags and ecosystem associations
   - Market data (when available)
5. **Saves Results** in multiple formats

### Output Files

The script creates several output files in the `./output` directory:

- `linea-projects-[timestamp].json` - Complete extraction results in JSON format
- `linea-projects-[timestamp]-summary.csv` - Project summary in CSV format
- `linea-projects-[timestamp]-details.csv` - Detailed project information in CSV format

### Example Output Structure

```json
{
  "ecosystem": {
    "ecosystem_id": 123,
    "ecosystem_name": "Linea",
    "project_num": 45
  },
  "projects": [
    {
      "project_id": 1234,
      "project_name": "Example Project",
      "logo": "https://...",
      "one_liner": "Project description"
    }
  ],
  "projectDetails": [
    {
      "project_id": 1234,
      "project_name": "Example Project",
      "token_symbol": "EXAMPLE",
      "establishment_date": "2023-01-01",
      "description": "Detailed description...",
      "total_funding": 1000000,
      "tags": ["DeFi", "DEX"],
      "investors": [...],
      "team_members": [...],
      "social_media": [...],
      "contracts": [...]
    }
  ],
  "extractionMetadata": {
    "timestamp": "2024-01-01T12:00:00.000Z",
    "totalProjects": 45,
    "successfulDetails": 43,
    "failedDetails": 2,
    "errors": [...]
  }
}
```

## API Endpoints Used

The extractor uses the following RootData API endpoints:

1. **GET Balance** (`/quotacredits`) - Check API key status and remaining credits
2. **Ecosystem Map** (`/ecosystem_map`) - Find Linea ecosystem ID
3. **Projects by Ecosystem** (`/projects_by_ecosystems`) - Get all Linea projects
4. **Project Details** (`/get_item`) - Get detailed information for each project

## Rate Limiting

The tool automatically handles rate limiting:

- **Limit**: 100 requests per minute (as per RootData API)
- **Implementation**: Built-in rate limiter with request queuing
- **Monitoring**: Real-time rate limit status reporting

## Error Handling

The tool handles various error scenarios:

- **Authentication Errors** (110): Invalid API key
- **Bad Request** (400): Invalid parameters
- **Forbidden** (403): Insufficient credits
- **Not Found** (404): Resource not found
- **Rate Limit** (410): Too many requests
- **Server Errors** (500): Internal server errors
- **Network Errors**: Connection issues, timeouts

## Logging

Comprehensive logging is provided:

- **Console Output**: Real-time progress and status
- **Log Files**: Detailed logs saved to `./logs/` directory
- **Error Tracking**: Separate error log for troubleshooting
- **Structured Logging**: JSON format for easy parsing

## Development

### Scripts

- `npm run build` - Compile TypeScript to JavaScript
- `npm run dev` - Run with ts-node for development
- `npm run test` - Run tests (when implemented)
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

### Project Structure

```
src/
├── client/           # API client implementation
├── config/           # Configuration management
├── errors/           # Custom error classes
├── extractors/       # Data extraction logic
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check your API key in the `.env` file
   - Verify the API key is valid and active

2. **Rate Limit Exceeded**
   - The tool automatically handles rate limiting
   - If you see this error, wait a minute and retry

3. **Insufficient Credits**
   - Check your RootData API plan and remaining credits
   - Upgrade your plan if necessary

4. **Network Errors**
   - Check your internet connection
   - Verify the RootData API is accessible

### Getting Help

1. Check the log files in `./logs/` for detailed error information
2. Review the console output for real-time status
3. Ensure all dependencies are installed correctly
4. Verify your API key and network connectivity

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.
