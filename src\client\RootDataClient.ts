/**
 * Main RootData API Client
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { RootDataConfig, ApiResponse } from '../types/api';
import { RateLimiter } from '../utils/RateLimiter';
import { RetryHandler } from '../utils/RetryHandler';
import { logger } from '../utils/Logger';
import {
  RootDataError,
  NetworkError,
  createErrorFromResponse
} from '../errors/RootDataError';
import { Validator } from '../utils/Validator';

export class RootDataClient {
  private readonly config: RootDataConfig;
  private readonly httpClient: AxiosInstance;
  private readonly rateLimiter: RateLimiter;
  private readonly retryHandler: RetryHandler;

  constructor(config: RootDataConfig) {
    this.config = config;
    this.rateLimiter = new RateLimiter(config.rateLimitRequestsPerMinute);
    this.retryHandler = new RetryHandler({
      maxRetries: config.maxRetries,
      baseDelayMs: config.retryDelayMs,
    });

    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'apikey': config.apiKey,
        'language': config.language || 'en',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        logger.info('Making API request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data ? JSON.stringify(config.data) : undefined,
        });
        return config;
      },
      (error) => {
        logger.error('Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.httpClient.interceptors.response.use(
      (response) => {
        logger.info('API response received', {
          status: response.status,
          url: response.config.url,
          result: response.data?.result,
        });
        return response;
      },
      (error) => {
        logger.error('API response error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a POST request to the RootData API with rate limiting and retry logic
   */
  private async makeRequest<T>(
    endpoint: string,
    data: any = {},
    operationName: string = endpoint
  ): Promise<T> {
    return this.retryHandler.execute(async () => {
      // Wait for rate limit slot
      await this.rateLimiter.waitForSlot();

      try {
        const response: AxiosResponse<ApiResponse<T>> = await this.httpClient.post(endpoint, data);
        
        // Check if the API returned an error
        if (response.data.result !== 200) {
          throw createErrorFromResponse(response.data.result, response.data.message);
        }

        return response.data.data;
      } catch (error) {
        if (axios.isAxiosError(error)) {
          if (error.response) {
            // Server responded with error status
            const apiResponse = error.response.data as ApiResponse<any>;
            if (apiResponse?.result && apiResponse.result !== 200) {
              throw createErrorFromResponse(apiResponse.result, apiResponse.message);
            }
            throw new RootDataError(
              `HTTP ${error.response.status}: ${error.response.statusText}`,
              error.response.status
            );
          } else if (error.request) {
            // Network error
            throw new NetworkError('Network error: No response received', error);
          } else {
            // Request setup error
            throw new NetworkError(`Request error: ${error.message}`, error);
          }
        }
        throw error;
      }
    }, operationName);
  }

  /**
   * Get current API key balance and usage information
   */
  public async getBalance() {
    logger.info('Getting API balance');
    return this.makeRequest('/quotacredits', {}, 'getBalance');
  }

  /**
   * Search for projects, VCs, or people
   */
  public async search(query: string, preciseXSearch: boolean = false) {
    const request = { query, precise_x_search: preciseXSearch };
    Validator.validateSearchRequest(request);

    logger.info('Searching', { query, preciseXSearch });
    return this.makeRequest('/ser_inv', request, 'search');
  }

  /**
   * Get ecosystem map list
   */
  public async getEcosystemMap() {
    logger.info('Getting ecosystem map');
    return this.makeRequest('/ecosystem_map', {}, 'getEcosystemMap');
  }

  /**
   * Get projects by ecosystem IDs
   */
  public async getProjectsByEcosystem(ecosystemIds: string) {
    const request = { ecosystem_ids: ecosystemIds };
    Validator.validateProjectsByEcosystemRequest(request);

    logger.info('Getting projects by ecosystem', { ecosystemIds });
    return this.makeRequest('/projects_by_ecosystems', request, 'getProjectsByEcosystem');
  }

  /**
   * Get detailed project information
   */
  public async getProjectDetails(
    projectId: number,
    includeTeam: boolean = true,
    includeInvestors: boolean = true
  ) {
    const request = {
      project_id: projectId,
      include_team: includeTeam,
      include_investors: includeInvestors,
    };
    Validator.validateProjectDetailsRequest(request);

    logger.info('Getting project details', { projectId, includeTeam, includeInvestors });
    return this.makeRequest('/get_item', request, 'getProjectDetails');
  }

  /**
   * Get ID list for projects, VCs, or people
   */
  public async getIdList(type: number) {
    const request = { type };
    Validator.validateIdMapRequest(request);

    logger.info('Getting ID list', { type });
    return this.makeRequest('/id_map', request, 'getIdList');
  }

  /**
   * Get rate limiter status
   */
  public getRateLimiterStatus() {
    return {
      currentRequests: this.rateLimiter.getCurrentRequestCount(),
      timeUntilNextSlot: this.rateLimiter.getTimeUntilNextSlot(),
      canMakeRequest: this.rateLimiter.canMakeRequest(),
    };
  }
}
