/**
 * Retry handler with exponential backoff for RootData API
 */

import { RootDataError, RateLimitError, InternalServerError, NetworkError } from '../errors/RootDataError';

export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

export class RetryHandler {
  private readonly config: RetryConfig;

  constructor(config: Partial<RetryConfig> = {}) {
    this.config = {
      maxRetries: config.maxRetries ?? 3,
      baseDelayMs: config.baseDelayMs ?? 1000,
      maxDelayMs: config.maxDelayMs ?? 30000,
      backoffMultiplier: config.backoffMultiplier ?? 2,
    };
  }

  /**
   * Execute a function with retry logic
   */
  public async execute<T>(
    operation: () => Promise<T>,
    operationName: string = 'operation'
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry for certain error types
        if (!this.shouldRetry(error as Error)) {
          throw error;
        }
        
        // Don't retry on the last attempt
        if (attempt === this.config.maxRetries) {
          break;
        }
        
        const delay = this.calculateDelay(attempt);
        console.warn(
          `${operationName} failed (attempt ${attempt + 1}/${this.config.maxRetries + 1}): ${
            (error as Error).message
          }. Retrying in ${delay}ms...`
        );
        
        await this.sleep(delay);
      }
    }
    
    throw lastError!;
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetry(error: Error): boolean {
    // Retry for network errors
    if (error instanceof NetworkError) {
      return true;
    }
    
    // Retry for rate limit errors (with backoff)
    if (error instanceof RateLimitError) {
      return true;
    }
    
    // Retry for internal server errors
    if (error instanceof InternalServerError) {
      return true;
    }
    
    // Don't retry for RootData API errors that indicate client issues
    if (error instanceof RootDataError) {
      return false;
    }
    
    // Retry for other unknown errors (might be network issues)
    return true;
  }

  /**
   * Calculate delay for exponential backoff
   */
  private calculateDelay(attempt: number): number {
    const delay = this.config.baseDelayMs * Math.pow(this.config.backoffMultiplier, attempt);
    return Math.min(delay, this.config.maxDelayMs);
  }

  /**
   * Sleep for the specified number of milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
