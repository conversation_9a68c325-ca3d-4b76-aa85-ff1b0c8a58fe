/**
 * Basic usage examples for RootData API client
 */

import { RootDataClient } from '../src/client/RootDataClient';
import { LineaExtractor } from '../src/extractors/LineaExtractor';
import { Config } from '../src/config/Config';
import { Logger } from '../src/utils/Logger';

async function basicUsageExample() {
  // Add console logging
  Logger.addConsoleTransport();
  
  // Get configuration
  const config = Config.getInstance().getConfig();
  
  // Create client
  const client = new RootDataClient(config);
  
  try {
    // Example 1: Check API balance
    console.log('=== Checking API Balance ===');
    const balance = await client.getBalance();
    console.log('Balance:', balance);
    
    // Example 2: Search for projects
    console.log('\n=== Searching for Ethereum projects ===');
    const searchResults = await client.search('ethereum');
    console.log('Search results:', searchResults.slice(0, 3)); // Show first 3 results
    
    // Example 3: Get ecosystem map
    console.log('\n=== Getting Ecosystem Map ===');
    const ecosystemMap = await client.getEcosystemMap();
    console.log('Total ecosystems:', ecosystemMap.length);
    console.log('Sample ecosystems:', ecosystemMap.slice(0, 5));
    
    // Example 4: Find Linea ecosystem
    console.log('\n=== Finding Linea Ecosystem ===');
    const lineaEcosystem = ecosystemMap.find(eco => 
      eco.ecosystem_name.toLowerCase().includes('linea')
    );
    
    if (lineaEcosystem) {
      console.log('Linea ecosystem found:', lineaEcosystem);
      
      // Example 5: Get Linea projects
      console.log('\n=== Getting Linea Projects ===');
      const lineaProjects = await client.getProjectsByEcosystem(
        lineaEcosystem.ecosystem_id.toString()
      );
      console.log(`Found ${lineaProjects.length} Linea projects`);
      console.log('Sample projects:', lineaProjects.slice(0, 3));
      
      // Example 6: Get detailed info for first project
      if (lineaProjects.length > 0) {
        console.log('\n=== Getting Project Details ===');
        const projectDetails = await client.getProjectDetails(
          lineaProjects[0].project_id,
          true, // include team
          true  // include investors
        );
        console.log('Project details:', {
          name: projectDetails.project_name,
          symbol: projectDetails.token_symbol,
          description: projectDetails.one_liner,
          funding: projectDetails.total_funding,
          tags: projectDetails.tags,
        });
      }
    } else {
      console.log('Linea ecosystem not found');
    }
    
    // Example 7: Rate limiter status
    console.log('\n=== Rate Limiter Status ===');
    const rateLimiterStatus = client.getRateLimiterStatus();
    console.log('Rate limiter status:', rateLimiterStatus);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

async function extractorUsageExample() {
  console.log('\n=== Using LineaExtractor ===');
  
  const config = Config.getInstance().getConfig();
  const client = new RootDataClient(config);
  const extractor = new LineaExtractor(client, './examples/output');
  
  try {
    const result = await extractor.extractLineaProjects();
    console.log('Extraction completed:', {
      ecosystem: result.ecosystem?.ecosystem_name,
      totalProjects: result.extractionMetadata.totalProjects,
      successfulDetails: result.extractionMetadata.successfulDetails,
    });
    
    await extractor.saveResults(result);
    console.log('Results saved to ./examples/output');
    
  } catch (error) {
    console.error('Extraction failed:', error);
  }
}

// Run examples
async function main() {
  console.log('RootData API Client Examples\n');
  
  try {
    await basicUsageExample();
    await extractorUsageExample();
  } catch (error) {
    console.error('Example failed:', error);
  }
}

if (require.main === module) {
  main();
}
