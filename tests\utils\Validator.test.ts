/**
 * Tests for Validator utility
 */

import { Validator } from '../../src/utils/Validator';

describe('Validator', () => {
  describe('validateSearchRequest', () => {
    it('should validate valid search request', () => {
      expect(() => {
        Validator.validateSearchRequest({ query: 'ethereum' });
      }).not.toThrow();
    });

    it('should throw for empty query', () => {
      expect(() => {
        Validator.validateSearchRequest({ query: '' });
      }).toThrow('Search query cannot be empty');
    });

    it('should throw for missing query', () => {
      expect(() => {
        Validator.validateSearchRequest({} as any);
      }).toThrow('Search query is required and must be a string');
    });

    it('should throw for too long query', () => {
      const longQuery = 'a'.repeat(1001);
      expect(() => {
        Validator.validateSearchRequest({ query: longQuery });
      }).toThrow('Search query is too long');
    });
  });

  describe('validateProjectDetailsRequest', () => {
    it('should validate request with project_id', () => {
      expect(() => {
        Validator.validateProjectDetailsRequest({ project_id: 123 });
      }).not.toThrow();
    });

    it('should validate request with contract_address', () => {
      expect(() => {
        Validator.validateProjectDetailsRequest({ contract_address: '0x123...' });
      }).not.toThrow();
    });

    it('should throw when both project_id and contract_address are provided', () => {
      expect(() => {
        Validator.validateProjectDetailsRequest({ 
          project_id: 123, 
          contract_address: '0x123...' 
        });
      }).toThrow('Cannot specify both project_id and contract_address');
    });

    it('should throw when neither project_id nor contract_address are provided', () => {
      expect(() => {
        Validator.validateProjectDetailsRequest({});
      }).toThrow('Either project_id or contract_address is required');
    });

    it('should throw for invalid project_id', () => {
      expect(() => {
        Validator.validateProjectDetailsRequest({ project_id: -1 });
      }).toThrow('project_id must be a positive integer');
    });
  });

  describe('validateIdMapRequest', () => {
    it('should validate valid type values', () => {
      [1, 2, 3].forEach(type => {
        expect(() => {
          Validator.validateIdMapRequest({ type });
        }).not.toThrow();
      });
    });

    it('should throw for invalid type values', () => {
      [0, 4, -1, 1.5].forEach(type => {
        expect(() => {
          Validator.validateIdMapRequest({ type });
        }).toThrow('type must be 1 (Project), 2 (VC), or 3 (People)');
      });
    });
  });

  describe('validateProjectsByEcosystemRequest', () => {
    it('should validate single ecosystem ID', () => {
      expect(() => {
        Validator.validateProjectsByEcosystemRequest({ ecosystem_ids: '123' });
      }).not.toThrow();
    });

    it('should validate multiple ecosystem IDs', () => {
      expect(() => {
        Validator.validateProjectsByEcosystemRequest({ ecosystem_ids: '123,456,789' });
      }).not.toThrow();
    });

    it('should throw for empty ecosystem_ids', () => {
      expect(() => {
        Validator.validateProjectsByEcosystemRequest({ ecosystem_ids: '' });
      }).toThrow('ecosystem_ids cannot be empty');
    });

    it('should throw for invalid ecosystem ID format', () => {
      expect(() => {
        Validator.validateProjectsByEcosystemRequest({ ecosystem_ids: '123,abc,456' });
      }).toThrow('Invalid ecosystem ID: abc');
    });
  });

  describe('validateApiKey', () => {
    it('should validate valid API key', () => {
      expect(() => {
        Validator.validateApiKey('valid-api-key-123');
      }).not.toThrow();
    });

    it('should throw for empty API key', () => {
      expect(() => {
        Validator.validateApiKey('');
      }).toThrow('API key cannot be empty');
    });

    it('should throw for too short API key', () => {
      expect(() => {
        Validator.validateApiKey('short');
      }).toThrow('API key appears to be too short');
    });
  });

  describe('validateLanguage', () => {
    it('should validate supported languages', () => {
      ['en', 'cn'].forEach(lang => {
        expect(() => {
          Validator.validateLanguage(lang);
        }).not.toThrow();
      });
    });

    it('should throw for unsupported language', () => {
      expect(() => {
        Validator.validateLanguage('fr');
      }).toThrow('Language must be either "en" or "cn"');
    });
  });
});
