/**
 * Configuration management for RootData API client
 */

import * as dotenv from 'dotenv';
import { RootDataConfig } from '../types/api';

// Load environment variables
dotenv.config();

export class Config {
  private static instance: Config;
  private config: RootDataConfig;

  private constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  public static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  public getConfig(): RootDataConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<RootDataConfig>): void {
    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }

  private loadConfig(): RootDataConfig {
    return {
      apiKey: process.env.ROOTDATA_API_KEY || '',
      language: (process.env.ROOTDATA_LANGUAGE as 'en' | 'cn') || 'en',
      baseUrl: process.env.ROOTDATA_BASE_URL || 'https://api.rootdata.com/open',
      timeout: parseInt(process.env.ROOTDATA_TIMEOUT || '30000'),
      rateLimitRequestsPerMinute: parseInt(process.env.RATE_LIMIT_REQUESTS_PER_MINUTE || '100'),
      maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
      retryDelayMs: parseInt(process.env.RETRY_DELAY_MS || '1000'),
    };
  }

  private validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error('ROOTDATA_API_KEY is required. Please set it in environment variables or .env file.');
    }

    if (this.config.language && !['en', 'cn'].includes(this.config.language)) {
      throw new Error('ROOTDATA_LANGUAGE must be either "en" or "cn"');
    }

    if (this.config.timeout && (this.config.timeout < 1000 || this.config.timeout > 300000)) {
      throw new Error('ROOTDATA_TIMEOUT must be between 1000 and 300000 milliseconds');
    }

    if (this.config.rateLimitRequestsPerMinute && this.config.rateLimitRequestsPerMinute < 1) {
      throw new Error('RATE_LIMIT_REQUESTS_PER_MINUTE must be at least 1');
    }

    if (this.config.maxRetries && this.config.maxRetries < 0) {
      throw new Error('MAX_RETRIES must be non-negative');
    }

    if (this.config.retryDelayMs && this.config.retryDelayMs < 0) {
      throw new Error('RETRY_DELAY_MS must be non-negative');
    }
  }
}

export const getConfig = (): RootDataConfig => Config.getInstance().getConfig();
