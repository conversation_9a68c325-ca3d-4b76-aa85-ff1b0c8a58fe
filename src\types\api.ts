/**
 * RootData API Types and Interfaces
 */

export interface RootDataConfig {
  apiKey: string;
  language?: 'en' | 'cn';
  baseUrl?: string;
  timeout?: number;
  rateLimitRequestsPerMinute?: number;
  maxRetries?: number;
  retryDelayMs?: number;
}

export interface ApiResponse<T> {
  data: T;
  result: number;
  message?: string;
}

export interface SearchRequest {
  query: string;
  precise_x_search?: boolean;
}

export interface SearchResult {
  id: number;
  type: number; // 1 Project; 2 VC; 3 People
  name: string;
  logo: string;
  introduce: string;
  active: boolean;
  rootdataurl: string;
}

export interface EcosystemMapResult {
  ecosystem_id: number;
  ecosystem_name: string;
  project_num: number;
}

export interface ProjectsByEcosystemRequest {
  ecosystem_ids: string; // comma-separated IDs
}

export interface ProjectSummary {
  project_id: number;
  project_name: string;
  logo: string;
  one_liner: string;
}

export interface ProjectDetailsRequest {
  project_id?: number;
  contract_address?: string;
  include_team?: boolean;
  include_investors?: boolean;
}

export interface SocialMedia {
  name: string;
  url: string;
}

export interface Investor {
  investor_id: number;
  investor_name: string;
  investor_logo: string;
  investor_type: string;
}

export interface TeamMember {
  people_id: number;
  people_name: string;
  people_logo: string;
  position: string;
}

export interface Contract {
  chain: string;
  contract_address: string;
  contract_type: string;
}

export interface ProjectDetails {
  project_id: number;
  project_name: string;
  logo: string;
  token_symbol: string;
  establishment_date: string;
  one_liner: string;
  description: string;
  active: boolean;
  total_funding: number;
  tags: string[];
  rootdataurl: string;
  investors?: Investor[];
  social_media?: SocialMedia[];
  similar_project?: ProjectSummary[];
  ecosystem?: string[];
  on_main_net?: string[];
  plan_to_launch?: string[];
  on_test_net?: string[];
  fully_diluted_market_cap?: string;
  market_cap?: string;
  price?: string;
  team_members?: TeamMember[];
  token_launch_time?: string;
  contracts?: Contract[];
}

export interface BalanceResult {
  apikey: string;
  start: number;
  end: number;
  level: string;
  total_credits: number;
  credits: number;
  last_mo_credits: number;
}

export interface IdMapRequest {
  type: number; // 1 Project; 2 VC; 3 People
}

export interface IdMapResult {
  id: number;
  name: string;
}
