{"name": "rootdata-linea-extractor", "version": "1.0.0", "description": "A comprehensive data extraction solution for retrieving Linea ecosystem projects from RootData API", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "extract-linea": "ts-node src/extractLinea.ts"}, "keywords": ["rootdata", "linea", "blockchain", "cryptocurrency", "data-extraction", "api-client"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "csv-writer": "^1.6.0", "fs-extra": "^11.1.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/jest": "^29.5.5", "@types/fs-extra": "^11.0.2", "typescript": "^5.2.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.51.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "prettier": "^3.0.3"}, "engines": {"node": ">=16.0.0"}}