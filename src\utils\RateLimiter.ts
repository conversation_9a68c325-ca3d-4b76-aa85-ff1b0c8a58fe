/**
 * Rate limiter implementation for RootData API
 * Ensures compliance with 100 requests per minute limit
 */

export class RateLimiter {
  private requestTimes: number[] = [];
  private readonly maxRequestsPerMinute: number;
  private readonly windowMs: number = 60 * 1000; // 1 minute in milliseconds

  constructor(maxRequestsPerMinute: number = 100) {
    this.maxRequestsPerMinute = maxRequestsPerMinute;
  }

  /**
   * Check if a request can be made now
   */
  public canMakeRequest(): boolean {
    this.cleanOldRequests();
    return this.requestTimes.length < this.maxRequestsPerMinute;
  }

  /**
   * Record a request and return the delay needed before the next request
   */
  public recordRequest(): number {
    const now = Date.now();
    this.cleanOldRequests();
    
    if (this.requestTimes.length >= this.maxRequestsPerMinute) {
      // Calculate delay until the oldest request expires
      const oldestRequest = this.requestTimes[0];
      const delay = this.windowMs - (now - oldestRequest);
      return Math.max(0, delay);
    }
    
    this.requestTimes.push(now);
    return 0;
  }

  /**
   * Wait for the appropriate delay before making a request
   */
  public async waitForSlot(): Promise<void> {
    const delay = this.recordRequest();
    if (delay > 0) {
      await this.sleep(delay);
      // Record the actual request time after waiting
      this.requestTimes[this.requestTimes.length - 1] = Date.now();
    }
  }

  /**
   * Get the current request count in the window
   */
  public getCurrentRequestCount(): number {
    this.cleanOldRequests();
    return this.requestTimes.length;
  }

  /**
   * Get the time until the next slot is available
   */
  public getTimeUntilNextSlot(): number {
    this.cleanOldRequests();
    
    if (this.requestTimes.length < this.maxRequestsPerMinute) {
      return 0;
    }
    
    const now = Date.now();
    const oldestRequest = this.requestTimes[0];
    return Math.max(0, this.windowMs - (now - oldestRequest));
  }

  /**
   * Reset the rate limiter
   */
  public reset(): void {
    this.requestTimes = [];
  }

  private cleanOldRequests(): void {
    const now = Date.now();
    const cutoff = now - this.windowMs;
    this.requestTimes = this.requestTimes.filter(time => time > cutoff);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
