#!/usr/bin/env node

/**
 * Main script to extract Linea ecosystem projects from RootData
 */

import { RootDataClient } from './client/RootDataClient';
import { LineaExtractor } from './extractors/LineaExtractor';
import { Config } from './config/Config';
import { Logger, logger } from './utils/Logger';
import { RootDataError } from './errors/RootDataError';

async function main() {
  try {
    // Add console logging for interactive use
    Logger.addConsoleTransport();
    
    logger.info('Starting Linea ecosystem extraction');
    
    // Load configuration
    const config = Config.getInstance().getConfig();
    
    // Override with provided API key if not set in environment
    if (!config.apiKey || config.apiKey === '') {
      config.apiKey = 'AtIYXUndk8xNYFzBs9LiGOUhv1z4wGXk';
      logger.info('Using provided API key');
    }
    
    logger.info('Configuration loaded', {
      baseUrl: config.baseUrl,
      language: config.language,
      rateLimitRequestsPerMinute: config.rateLimitRequestsPerMinute,
      maxRetries: config.maxRetries,
    });

    // Create RootData client
    const client = new RootDataClient(config);
    
    // Test API connection and get balance
    logger.info('Testing API connection...');
    try {
      const balance = await client.getBalance();
      logger.info('API connection successful', {
        level: balance.level,
        credits: balance.credits,
        totalCredits: balance.total_credits,
      });
    } catch (error) {
      logger.error('API connection test failed', { error: (error as Error).message });
      if (error instanceof RootDataError) {
        logger.error('API Error Details', {
          code: error.code,
          message: error.message,
        });
      }
      throw error;
    }

    // Create extractor
    const outputDir = process.env.OUTPUT_DIRECTORY || './output';
    const extractor = new LineaExtractor(client, outputDir);

    // Extract Linea projects
    logger.info('Starting Linea project extraction...');
    const startTime = Date.now();
    
    const result = await extractor.extractLineaProjects();
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    // Save results
    logger.info('Saving extraction results...');
    await extractor.saveResults(result);

    // Print summary
    console.log('\n=== EXTRACTION SUMMARY ===');
    console.log(`Ecosystem: ${result.ecosystem?.ecosystem_name} (ID: ${result.ecosystem?.ecosystem_id})`);
    console.log(`Total projects found: ${result.extractionMetadata.totalProjects}`);
    console.log(`Successful detail extractions: ${result.extractionMetadata.successfulDetails}`);
    console.log(`Failed detail extractions: ${result.extractionMetadata.failedDetails}`);
    console.log(`Extraction duration: ${duration.toFixed(2)} seconds`);
    
    if (result.extractionMetadata.errors.length > 0) {
      console.log('\nErrors encountered:');
      result.extractionMetadata.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    // Show rate limiter status
    const rateLimiterStatus = client.getRateLimiterStatus();
    console.log('\nRate Limiter Status:');
    console.log(`Current requests in window: ${rateLimiterStatus.currentRequests}`);
    console.log(`Time until next slot: ${rateLimiterStatus.timeUntilNextSlot}ms`);

    logger.info('Extraction completed successfully');
    
  } catch (error) {
    logger.error('Extraction failed', { 
      error: (error as Error).message,
      stack: (error as Error).stack,
    });
    
    console.error('\n=== EXTRACTION FAILED ===');
    console.error(`Error: ${(error as Error).message}`);
    
    if (error instanceof RootDataError) {
      console.error(`Error Code: ${error.code}`);
      if (error.code === 110) {
        console.error('Authentication failed. Please check your API key.');
      } else if (error.code === 403) {
        console.error('Insufficient credits. Please check your API plan.');
      } else if (error.code === 410) {
        console.error('Rate limit exceeded. Please wait before retrying.');
      }
    }
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', { promise, reason });
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', { error: error.message, stack: error.stack });
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main();
}
