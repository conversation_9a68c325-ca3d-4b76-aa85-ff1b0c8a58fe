/**
 * Validation utilities for RootData API requests and responses
 */

import { 
  SearchRequest, 
  ProjectDetailsRequest, 
  IdMapRequest,
  ProjectsByEcosystemRequest 
} from '../types/api';

export class Validator {
  /**
   * Validate search request parameters
   */
  public static validateSearchRequest(request: SearchRequest): void {
    if (!request.query || typeof request.query !== 'string') {
      throw new Error('Search query is required and must be a string');
    }
    
    if (request.query.trim().length === 0) {
      throw new Error('Search query cannot be empty');
    }
    
    if (request.query.length > 1000) {
      throw new Error('Search query is too long (max 1000 characters)');
    }
    
    if (request.precise_x_search !== undefined && typeof request.precise_x_search !== 'boolean') {
      throw new Error('precise_x_search must be a boolean');
    }
  }

  /**
   * Validate project details request parameters
   */
  public static validateProjectDetailsRequest(request: ProjectDetailsRequest): void {
    if (!request.project_id && !request.contract_address) {
      throw new Error('Either project_id or contract_address is required');
    }
    
    if (request.project_id && request.contract_address) {
      throw new Error('Cannot specify both project_id and contract_address');
    }
    
    if (request.project_id !== undefined) {
      if (!Number.isInteger(request.project_id) || request.project_id <= 0) {
        throw new Error('project_id must be a positive integer');
      }
    }
    
    if (request.contract_address !== undefined) {
      if (typeof request.contract_address !== 'string' || request.contract_address.trim().length === 0) {
        throw new Error('contract_address must be a non-empty string');
      }
    }
    
    if (request.include_team !== undefined && typeof request.include_team !== 'boolean') {
      throw new Error('include_team must be a boolean');
    }
    
    if (request.include_investors !== undefined && typeof request.include_investors !== 'boolean') {
      throw new Error('include_investors must be a boolean');
    }
  }

  /**
   * Validate ID map request parameters
   */
  public static validateIdMapRequest(request: IdMapRequest): void {
    if (!request.type) {
      throw new Error('type is required');
    }
    
    if (!Number.isInteger(request.type) || ![1, 2, 3].includes(request.type)) {
      throw new Error('type must be 1 (Project), 2 (VC), or 3 (People)');
    }
  }

  /**
   * Validate projects by ecosystem request parameters
   */
  public static validateProjectsByEcosystemRequest(request: ProjectsByEcosystemRequest): void {
    if (!request.ecosystem_ids || typeof request.ecosystem_ids !== 'string') {
      throw new Error('ecosystem_ids is required and must be a string');
    }
    
    if (request.ecosystem_ids.trim().length === 0) {
      throw new Error('ecosystem_ids cannot be empty');
    }
    
    // Validate that ecosystem_ids contains valid comma-separated numbers
    const ids = request.ecosystem_ids.split(',').map(id => id.trim());
    
    for (const id of ids) {
      if (!/^\d+$/.test(id)) {
        throw new Error(`Invalid ecosystem ID: ${id}. All IDs must be positive integers`);
      }
      
      const numId = parseInt(id, 10);
      if (numId <= 0) {
        throw new Error(`Invalid ecosystem ID: ${id}. All IDs must be positive integers`);
      }
    }
  }

  /**
   * Validate API key format
   */
  public static validateApiKey(apiKey: string): void {
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('API key is required and must be a string');
    }
    
    if (apiKey.trim().length === 0) {
      throw new Error('API key cannot be empty');
    }
    
    // Basic format validation (adjust based on actual RootData API key format)
    if (apiKey.length < 10) {
      throw new Error('API key appears to be too short');
    }
  }

  /**
   * Validate language parameter
   */
  public static validateLanguage(language: string): void {
    if (!['en', 'cn'].includes(language)) {
      throw new Error('Language must be either "en" or "cn"');
    }
  }

  /**
   * Sanitize and validate ecosystem name for search
   */
  public static sanitizeEcosystemName(name: string): string {
    if (!name || typeof name !== 'string') {
      throw new Error('Ecosystem name must be a string');
    }
    
    const sanitized = name.trim().toLowerCase();
    
    if (sanitized.length === 0) {
      throw new Error('Ecosystem name cannot be empty');
    }
    
    return sanitized;
  }

  /**
   * Validate project ID
   */
  public static validateProjectId(projectId: number): void {
    if (!Number.isInteger(projectId) || projectId <= 0) {
      throw new Error('Project ID must be a positive integer');
    }
  }

  /**
   * Validate ecosystem ID
   */
  public static validateEcosystemId(ecosystemId: number): void {
    if (!Number.isInteger(ecosystemId) || ecosystemId <= 0) {
      throw new Error('Ecosystem ID must be a positive integer');
    }
  }
}
