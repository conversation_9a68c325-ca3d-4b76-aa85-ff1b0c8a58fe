/**
 * Linea Ecosystem Data Extractor
 */

import { RootDataClient } from '../client/RootDataClient';
import { logger } from '../utils/Logger';
import { 
  EcosystemMapResult, 
  ProjectSummary, 
  ProjectDetails 
} from '../types/api';
import * as fs from 'fs-extra';
import * as path from 'path';

export interface LineaExtractionResult {
  ecosystem: EcosystemMapResult | null;
  projects: ProjectSummary[];
  projectDetails: ProjectDetails[];
  extractionMetadata: {
    timestamp: string;
    totalProjects: number;
    successfulDetails: number;
    failedDetails: number;
    errors: string[];
  };
}

export class LineaExtractor {
  private client: RootDataClient;
  private outputDir: string;

  constructor(client: RootDataClient, outputDir: string = './output') {
    this.client = client;
    this.outputDir = outputDir;
  }

  /**
   * Extract all Linea ecosystem projects
   */
  public async extractLineaProjects(): Promise<LineaExtractionResult> {
    logger.info('Starting Linea ecosystem extraction');
    
    const result: LineaExtractionResult = {
      ecosystem: null,
      projects: [],
      projectDetails: [],
      extractionMetadata: {
        timestamp: new Date().toISOString(),
        totalProjects: 0,
        successfulDetails: 0,
        failedDetails: 0,
        errors: [],
      },
    };

    try {
      // Step 1: Find Linea ecosystem ID
      logger.info('Step 1: Finding Linea ecosystem ID');
      const lineaEcosystem = await this.findLineaEcosystem();
      
      if (!lineaEcosystem) {
        throw new Error('Linea ecosystem not found in ecosystem map');
      }
      
      result.ecosystem = lineaEcosystem;
      logger.info('Found Linea ecosystem', { 
        id: lineaEcosystem.ecosystem_id, 
        name: lineaEcosystem.ecosystem_name,
        projectCount: lineaEcosystem.project_num 
      });

      // Step 2: Get all projects in Linea ecosystem
      logger.info('Step 2: Getting all Linea projects');
      const projects = await this.getLineaProjects(lineaEcosystem.ecosystem_id);
      result.projects = projects;
      result.extractionMetadata.totalProjects = projects.length;
      
      logger.info(`Found ${projects.length} projects in Linea ecosystem`);

      // Step 3: Get detailed information for each project
      logger.info('Step 3: Getting detailed project information');
      const projectDetails = await this.getProjectDetails(projects);
      result.projectDetails = projectDetails.successful;
      result.extractionMetadata.successfulDetails = projectDetails.successful.length;
      result.extractionMetadata.failedDetails = projectDetails.failed.length;
      result.extractionMetadata.errors = projectDetails.errors;

      logger.info('Extraction completed', {
        totalProjects: result.extractionMetadata.totalProjects,
        successfulDetails: result.extractionMetadata.successfulDetails,
        failedDetails: result.extractionMetadata.failedDetails,
      });

      return result;
    } catch (error) {
      logger.error('Extraction failed', { error: (error as Error).message });
      result.extractionMetadata.errors.push((error as Error).message);
      throw error;
    }
  }

  /**
   * Find Linea ecosystem in the ecosystem map
   */
  private async findLineaEcosystem(): Promise<EcosystemMapResult | null> {
    try {
      const ecosystemMap: EcosystemMapResult[] = await this.client.getEcosystemMap();
      
      // Look for Linea (case-insensitive)
      const lineaEcosystem = ecosystemMap.find(ecosystem => 
        ecosystem.ecosystem_name.toLowerCase().includes('linea')
      );
      
      return lineaEcosystem || null;
    } catch (error) {
      logger.error('Failed to get ecosystem map', { error: (error as Error).message });
      throw error;
    }
  }

  /**
   * Get all projects in Linea ecosystem
   */
  private async getLineaProjects(ecosystemId: number): Promise<ProjectSummary[]> {
    try {
      const projects: ProjectSummary[] = await this.client.getProjectsByEcosystem(
        ecosystemId.toString()
      );
      return projects;
    } catch (error) {
      logger.error('Failed to get Linea projects', { error: (error as Error).message });
      throw error;
    }
  }

  /**
   * Get detailed information for multiple projects
   */
  private async getProjectDetails(projects: ProjectSummary[]): Promise<{
    successful: ProjectDetails[];
    failed: number;
    errors: string[];
  }> {
    const successful: ProjectDetails[] = [];
    const errors: string[] = [];
    let failed = 0;

    logger.info(`Getting details for ${projects.length} projects`);

    for (let i = 0; i < projects.length; i++) {
      const project = projects[i];
      
      try {
        logger.info(`Getting details for project ${i + 1}/${projects.length}: ${project.project_name}`);
        
        const details: ProjectDetails = await this.client.getProjectDetails(
          project.project_id,
          true, // include team
          true  // include investors
        );
        
        successful.push(details);
        logger.info(`Successfully got details for ${project.project_name}`);
        
        // Add a small delay to be respectful to the API
        if (i < projects.length - 1) {
          await this.sleep(100);
        }
        
      } catch (error) {
        failed++;
        const errorMessage = `Failed to get details for ${project.project_name}: ${(error as Error).message}`;
        errors.push(errorMessage);
        logger.warn(errorMessage);
      }
    }

    return { successful, failed, errors };
  }

  /**
   * Save extraction results to files
   */
  public async saveResults(result: LineaExtractionResult): Promise<void> {
    await fs.ensureDir(this.outputDir);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const baseFilename = `linea-projects-${timestamp}`;
    
    // Save as JSON
    const jsonPath = path.join(this.outputDir, `${baseFilename}.json`);
    await fs.writeJson(jsonPath, result, { spaces: 2 });
    logger.info(`Results saved to ${jsonPath}`);
    
    // Save as CSV (simplified version)
    await this.saveAsCSV(result, baseFilename);
  }

  /**
   * Save results as CSV
   */
  private async saveAsCSV(result: LineaExtractionResult, baseFilename: string): Promise<void> {
    const createCsvWriter = require('csv-writer').createObjectCsvWriter;
    
    // Projects summary CSV
    const projectsCsvPath = path.join(this.outputDir, `${baseFilename}-summary.csv`);
    const projectsCsvWriter = createCsvWriter({
      path: projectsCsvPath,
      header: [
        { id: 'project_id', title: 'Project ID' },
        { id: 'project_name', title: 'Project Name' },
        { id: 'one_liner', title: 'Description' },
        { id: 'logo', title: 'Logo URL' },
      ]
    });
    
    await projectsCsvWriter.writeRecords(result.projects);
    logger.info(`Project summary saved to ${projectsCsvPath}`);
    
    // Detailed projects CSV
    if (result.projectDetails.length > 0) {
      const detailsCsvPath = path.join(this.outputDir, `${baseFilename}-details.csv`);
      const detailsCsvWriter = createCsvWriter({
        path: detailsCsvPath,
        header: [
          { id: 'project_id', title: 'Project ID' },
          { id: 'project_name', title: 'Project Name' },
          { id: 'token_symbol', title: 'Token Symbol' },
          { id: 'establishment_date', title: 'Establishment Date' },
          { id: 'one_liner', title: 'One Liner' },
          { id: 'description', title: 'Description' },
          { id: 'total_funding', title: 'Total Funding' },
          { id: 'tags', title: 'Tags' },
          { id: 'active', title: 'Active' },
          { id: 'rootdataurl', title: 'RootData URL' },
        ]
      });
      
      const csvData = result.projectDetails.map(project => ({
        ...project,
        tags: Array.isArray(project.tags) ? project.tags.join(', ') : project.tags,
      }));
      
      await detailsCsvWriter.writeRecords(csvData);
      logger.info(`Project details saved to ${detailsCsvPath}`);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
