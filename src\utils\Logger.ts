/**
 * Logging utility for RootData API client
 */

import winston from 'winston';
import * as path from 'path';
import * as fs from 'fs-extra';

export class Logger {
  private static instance: winston.Logger;

  public static getInstance(): winston.Logger {
    if (!Logger.instance) {
      Logger.instance = Logger.createLogger();
    }
    return Logger.instance;
  }

  private static createLogger(): winston.Logger {
    // Ensure logs directory exists
    const logDir = process.env.LOG_DIRECTORY || './logs';
    fs.ensureDirSync(logDir);

    const logLevel = process.env.LOG_LEVEL || 'info';
    const logFile = process.env.LOG_FILE || path.join(logDir, 'rootdata-extractor.log');

    return winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'rootdata-extractor' },
      transports: [
        // Write all logs with level 'error' and below to error.log
        new winston.transports.File({
          filename: path.join(logDir, 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        // Write all logs to the main log file
        new winston.transports.File({
          filename: logFile,
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
      ],
    });
  }

  public static addConsoleTransport(): void {
    const logger = Logger.getInstance();
    
    // Check if console transport already exists
    const hasConsole = logger.transports.some(
      transport => transport instanceof winston.transports.Console
    );
    
    if (!hasConsole) {
      logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple(),
          winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
          })
        )
      }));
    }
  }
}

// Export a default logger instance
export const logger = Logger.getInstance();
