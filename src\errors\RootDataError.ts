/**
 * Custom error classes for RootData API
 */

export class RootDataError extends Error {
  public readonly code: number;
  public readonly originalError?: Error;

  constructor(message: string, code: number, originalError?: Error) {
    super(message);
    this.name = 'RootDataError';
    this.code = code;
    this.originalError = originalError;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, RootDataError);
    }
  }
}

export class AuthenticationError extends RootDataError {
  constructor(message: string = 'Authentication failed') {
    super(message, 110);
    this.name = 'AuthenticationError';
  }
}

export class BadRequestError extends RootDataError {
  constructor(message: string = 'The request parameters are invalid or missing') {
    super(message, 400);
    this.name = 'BadRequestError';
  }
}

export class ForbiddenError extends RootDataError {
  constructor(message: string = 'Access denied, insufficient remaining credits') {
    super(message, 403);
    this.name = 'ForbiddenError';
  }
}

export class NotFoundError extends RootDataError {
  constructor(message: string = 'No matching information found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

export class RateLimitError extends RootDataError {
  constructor(message: string = 'Your access frequency is too high, please wait one minute before resetting') {
    super(message, 410);
    this.name = 'RateLimitError';
  }
}

export class InternalServerError extends RootDataError {
  constructor(message: string = 'Internal server error') {
    super(message, 500);
    this.name = 'InternalServerError';
  }
}

export class NetworkError extends RootDataError {
  constructor(message: string, originalError?: Error) {
    super(message, -1, originalError);
    this.name = 'NetworkError';
  }
}

export function createErrorFromResponse(code: number, message?: string): RootDataError {
  switch (code) {
    case 110:
      return new AuthenticationError(message);
    case 400:
      return new BadRequestError(message);
    case 403:
      return new ForbiddenError(message);
    case 404:
      return new NotFoundError(message);
    case 410:
      return new RateLimitError(message);
    case 500:
      return new InternalServerError(message);
    default:
      return new RootDataError(message || 'Unknown error', code);
  }
}
