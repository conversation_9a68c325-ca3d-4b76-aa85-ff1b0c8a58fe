@echo off
REM RootData Linea Extractor - Installation and Run Script for Windows
REM This script sets up the environment and runs the Linea extraction

echo 🚀 RootData Linea Extractor Setup
echo ==================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 16+ and try again.
    pause
    exit /b 1
)

echo ✅ Node.js detected
node --version

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm and try again.
    pause
    exit /b 1
)

echo ✅ npm detected
npm --version

REM Install dependencies
echo.
echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Create necessary directories
echo.
echo 📁 Creating directories...
if not exist "output" mkdir output
if not exist "logs" mkdir logs

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo.
    echo ⚙️  Creating .env file...
    copy .env.example .env
    echo ✅ .env file created. You can modify it if needed.
) else (
    echo ✅ .env file already exists
)

REM Build the project
echo.
echo 🔨 Building TypeScript...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build project
    pause
    exit /b 1
)

REM Run the extraction
echo.
echo 🎯 Starting Linea ecosystem extraction...
echo This may take a few minutes depending on the number of projects...
echo.

npm run extract-linea
if %errorlevel% neq 0 (
    echo ❌ Extraction failed
    pause
    exit /b 1
)

echo.
echo 🎉 Extraction completed!
echo.
echo 📊 Results saved to:
echo    - JSON format: .\output\linea-projects-*.json
echo    - CSV format: .\output\linea-projects-*.csv
echo.
echo 📝 Logs saved to:
echo    - Main log: .\logs\rootdata-extractor.log
echo    - Error log: .\logs\error.log
echo.
echo ✨ Done! Check the output directory for your Linea ecosystem data.
echo.
pause
