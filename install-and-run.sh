#!/bin/bash

# RootData Linea Extractor - Installation and Run Script
# This script sets up the environment and runs the Linea extraction

set -e

echo "🚀 RootData Linea Extractor Setup"
echo "=================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ npm $(npm -v) detected"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Create necessary directories
echo ""
echo "📁 Creating directories..."
mkdir -p output
mkdir -p logs

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo ""
    echo "⚙️  Creating .env file..."
    cp .env.example .env
    echo "✅ .env file created. You can modify it if needed."
else
    echo "✅ .env file already exists"
fi

# Build the project
echo ""
echo "🔨 Building TypeScript..."
npm run build

# Run the extraction
echo ""
echo "🎯 Starting Linea ecosystem extraction..."
echo "This may take a few minutes depending on the number of projects..."
echo ""

npm run extract-linea

echo ""
echo "🎉 Extraction completed!"
echo ""
echo "📊 Results saved to:"
echo "   - JSON format: ./output/linea-projects-*.json"
echo "   - CSV format: ./output/linea-projects-*.csv"
echo ""
echo "📝 Logs saved to:"
echo "   - Main log: ./logs/rootdata-extractor.log"
echo "   - Error log: ./logs/error.log"
echo ""
echo "✨ Done! Check the output directory for your Linea ecosystem data."
